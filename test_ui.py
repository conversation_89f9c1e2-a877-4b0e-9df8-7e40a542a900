#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试现代化界面
"""

import sys
import os

def test_imports():
    """测试导入"""
    try:
        import customtkinter as ctk
        print("✅ CustomTkinter 导入成功")
        
        import tkinter as tk
        print("✅ Tkinter 导入成功")
        
        import requests
        print("✅ Requests 导入成功")
        
        from PIL import Image, ImageTk
        print("✅ Pillow 导入成功")
        
        import json
        print("✅ JSON 导入成功")
        
        import threading
        print("✅ Threading 导入成功")
        
        import datetime
        print("✅ Datetime 导入成功")
        
        import base64
        print("✅ Base64 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_ui_creation():
    """测试界面创建"""
    try:
        import customtkinter as ctk
        
        # 设置外观模式
        ctk.set_appearance_mode("system")
        ctk.set_default_color_theme("blue")
        
        # 创建测试窗口
        root = ctk.CTk()
        root.title("🖼️ 界面测试")
        root.geometry("800x600")
        
        # 创建测试标签
        label = ctk.CTkLabel(
            root,
            text="🎉 现代化界面测试成功！\n\n界面组件工作正常",
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=20, weight="bold")
        )
        label.pack(expand=True)
        
        # 创建关闭按钮
        close_btn = ctk.CTkButton(
            root,
            text="关闭测试",
            command=root.destroy,
            font=ctk.CTkFont(family="Microsoft YaHei UI", size=14),
            height=40,
            width=120
        )
        close_btn.pack(pady=20)
        
        print("✅ 界面创建成功")
        
        # 显示窗口2秒后自动关闭
        root.after(2000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 界面创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 开始测试现代化界面...")
    print()
    
    # 测试导入
    print("1. 测试依赖包导入:")
    if not test_imports():
        print("\n❌ 依赖包测试失败，请检查安装")
        return False
    
    print("\n2. 测试界面创建:")
    if not test_ui_creation():
        print("\n❌ 界面测试失败")
        return False
    
    print("\n✅ 所有测试通过！现代化界面可以正常使用")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
