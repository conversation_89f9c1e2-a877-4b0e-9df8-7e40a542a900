# 🖼️ 智能图像对比分析工具 - Enhanced Edition v2.0

基于 Grok-2-Vision API 的现代化图像对比分析工具，采用 CustomTkinter 构建的美观现代化界面。

## ✨ 新功能特性

### 🎨 界面美化
- **现代化设计**: 采用 CustomTkinter 框架，支持明暗主题切换
- **渐变背景**: 美观的渐变色标题区域
- **卡片式布局**: 清晰的模块化界面设计
- **动态边框**: 智能的状态指示边框颜色
- **悬停效果**: 丰富的交互反馈

### 🚀 功能增强
- **拖拽支持**: 支持直接拖拽图片到预览区域
- **实时预览**: 高质量图片预览，显示详细信息
- **API测试**: 内置API连接测试功能
- **预设提示词**: 三种预设的分析模式
- **结果导出**: 支持导出分析结果为文本文件
- **键盘快捷键**: 支持常用操作的快捷键

### 📊 状态指示
- **配置状态**: 实时显示API密钥配置状态
- **处理进度**: 详细的处理状态和进度显示
- **错误提示**: 友好的错误信息和解决建议

## 🎯 工作模式

### 🖼️ 单例模式
- 选择两张图片进行对比分析
- 实时显示分析结果
- 支持结果导出

### 📁 批量模式
- 批量处理多组图片
- 自动匹配同名文件
- 结果自动保存为txt文件

## ⌨️ 键盘快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl+O` | 选择图片A |
| `Ctrl+P` | 选择图片B |
| `F5` | 开始对比分析 |
| `Ctrl+Enter` | 开始对比分析 |
| `Ctrl+S` | 保存配置 |
| `Esc` | 退出程序 |

## 🛠️ 安装和使用

### 环境要求
- Python 3.8+
- 依赖包：customtkinter, requests, Pillow, darkdetect

### 快速启动
1. 运行 `start_enhanced.bat` (Windows)
2. 或直接运行 `python image_comparison_tool_modern.py`

### 配置说明
1. **API密钥**: 输入您的 Grok API 密钥 (格式: xai-...)
2. **系统提示词**: 选择预设或自定义分析提示词
3. **工作模式**: 选择单例或批量处理模式

## 🎨 界面预览

### 主界面特性
- **渐变标题栏**: 美观的渐变色背景
- **卡片式配置区**: 清晰的配置选项布局
- **模式选择卡片**: 直观的工作模式选择
- **拖拽预览区**: 支持拖拽的图片预览区域
- **多功能按钮**: 丰富的操作按钮
- **标签页结果**: 分类显示日志和结果

### 视觉改进
- **圆角设计**: 现代化的圆角元素
- **阴影效果**: 立体感的界面元素
- **状态颜色**: 直观的颜色状态指示
- **字体优化**: 清晰易读的字体选择

## 📝 更新日志

### v2.0 Enhanced Edition
- ✅ 全新的 CustomTkinter 界面
- ✅ 拖拽功能支持
- ✅ API连接测试
- ✅ 预设提示词
- ✅ 结果导出功能
- ✅ 键盘快捷键
- ✅ 状态指示优化
- ✅ 错误处理改进
- ✅ 界面美化和动画效果

### v1.0 Original
- ✅ 基础图像对比功能
- ✅ 单例和批量模式
- ✅ Tkinter 界面

## 🔧 技术栈

- **界面框架**: CustomTkinter
- **图像处理**: Pillow (PIL)
- **网络请求**: Requests
- **主题检测**: DarkDetect
- **API服务**: Grok-2-Vision

## 📞 支持

如有问题或建议，请查看日志信息或联系开发者。

---

**享受智能图像分析的乐趣！** 🎉
