#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化图像对比工具 - 基于 CustomTkinter
使用 Grok-2-Vision API 进行智能图像对比分析
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import requests
import json
import os
import threading
from PIL import Image, ImageTk
import base64
import datetime
import sys
import subprocess

# 设置DPI感知（修复Windows DPI缩放问题）
import os
os.environ['CTK_DISABLE_DPI_AWARENESS'] = '1'

# 设置外观模式和颜色主题
try:
    ctk.set_appearance_mode("light")  # 使用light模式更稳定
    ctk.set_default_color_theme("blue")  # 蓝色主题

    # 禁用一些可能导致问题的功能
    ctk.deactivate_automatic_dpi_awareness()
except Exception:
    # 如果设置失败，使用默认设置
    pass

# 自定义颜色方案
COLORS = {
    "primary": "#3b82f6",
    "primary_hover": "#2563eb",
    "secondary": "#6366f1",
    "secondary_hover": "#4f46e5",
    "success": "#22c55e",
    "success_hover": "#16a34a",
    "warning": "#f59e0b",
    "warning_hover": "#d97706",
    "error": "#ef4444",
    "error_hover": "#dc2626",
    "background": "#f8fafc",
    "card": "#ffffff",
    "card_hover": "#f1f5f9",
    "text": "#1e293b",
    "text_secondary": "#64748b",
    "border": "#e2e8f0",
    "border_focus": "#3b82f6"
}

# 深色模式颜色
COLORS_DARK = {
    "primary": "#3b82f6",
    "primary_hover": "#60a5fa",
    "secondary": "#818cf8",
    "secondary_hover": "#a5b4fc",
    "success": "#4ade80",
    "success_hover": "#6ee7b7",
    "warning": "#fbbf24",
    "warning_hover": "#fcd34d",
    "error": "#f87171",
    "error_hover": "#fca5a5",
    "background": "#0f172a",
    "card": "#1e293b",
    "card_hover": "#334155",
    "text": "#f1f5f9",
    "text_secondary": "#94a3b8",
    "border": "#334155",
    "border_focus": "#60a5fa"
}

class ModernImageComparisonTool:
    def __init__(self):
        try:
            # 创建主窗口
            self.root = ctk.CTk()
            self.root.title("🖼️ 智能图像对比分析 - Grok-2-Vision")
            self.root.geometry("1600x1100")
            self.root.minsize(1300, 900)

            # 设置窗口图标和样式
            try:
                # 如果有图标文件，可以设置
                # self.root.iconbitmap("icon.ico")
                pass
            except:
                pass

            # 设置窗口为完全不透明
            try:
                self.root.attributes('-alpha', 1.0)
            except:
                pass  # 忽略不支持的属性

            # 居中显示窗口
            self.center_window()

            # 初始化变量
            self.image_a_path = None
            self.image_b_path = None
            self.processing = False

            # 加载配置
            self.config = self.load_config()

            # API配置
            self.api_url = "https://api.x.ai/v1/chat/completions"

            # 创建界面
            self.create_widgets()

            # 加载保存的配置
            self.load_saved_config()

            # 设置窗口关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            # 添加键盘快捷键
            self.setup_shortcuts()

        except Exception as e:
            print(f"初始化失败: {e}")
            import traceback
            traceback.print_exc()
            # 如果CustomTkinter失败，尝试使用原版
            print("尝试启动原版界面...")
            import subprocess
            subprocess.run([sys.executable, "image_comparison_tool.py"])
            sys.exit(1)
    
    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def setup_shortcuts(self):
        """设置键盘快捷键"""
        self.root.bind('<Control-o>', lambda e: self.browse_image_a())
        self.root.bind('<Control-p>', lambda e: self.browse_image_b())
        self.root.bind('<Control-Return>', lambda e: self.generate_comparison())
        self.root.bind('<F5>', lambda e: self.generate_comparison())
        self.root.bind('<Escape>', lambda e: self.on_closing())
        self.root.bind('<Control-s>', lambda e: self.save_config_from_ui())
    
    def load_config(self):
        """加载配置文件"""
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            return {
                "api_key": "",
                "system_prompt": "请对比这两张图片，详细描述它们的差异和相似之处。",
                "batch_mode": {
                    "folder_a": "",
                    "folder_b": ""
                }
            }
    
    def save_config(self):
        """保存配置到文件"""
        with open("config.json", "w", encoding="utf-8") as f:
            json.dump(self.config, f, ensure_ascii=False, indent=4)
    
    def create_widgets(self):
        """创建现代化界面"""
        # 创建主滚动框架
        self.main_frame = ctk.CTkScrollableFrame(self.root, corner_radius=0)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # 标题区域
        self.create_header()

        # 配置区域
        self.create_config_section()

        # 模式选择区域（使用TAB）
        self.create_mode_section()

        # 工作区域（图片上传，在TAB中）
        self.create_work_section()

        # 操作按钮区域
        self.create_action_section()

        # 信息显示区域
        self.create_info_section()
    
    def create_header(self):
        """创建标题区域"""
        # 标题框架
        title_frame = ctk.CTkFrame(self.main_frame, fg_color="transparent")
        title_frame.pack(fill="x", pady=(0, 30))

        # 创建渐变标题背景
        header_bg = ctk.CTkFrame(
            title_frame,
            corner_radius=20,
            height=140,
            fg_color=("#667eea", "#1e293b"),
            border_width=2,
            border_color=("gray80", "gray30")
        )
        header_bg.pack(fill="x", padx=15, pady=(0, 25))

        # 主标题
        title_label = ctk.CTkLabel(
            header_bg,
            text="🖼️ 智能图像对比分析",
            font=ctk.CTkFont(size=36, weight="bold"),
            text_color=("white", "white")
        )
        title_label.pack(pady=(30, 8))

        # 副标题
        subtitle_label = ctk.CTkLabel(
            header_bg,
            text="基于 Grok-2-Vision API 的高级图像分析与对比系统 | 支持单例与批量处理",
            font=ctk.CTkFont(size=16),
            text_color=("gray90", "gray90")
        )
        subtitle_label.pack(pady=(0, 30))

        # 添加版本信息
        version_label = ctk.CTkLabel(
            header_bg,
            text="v2.0 Enhanced Edition",
            font=ctk.CTkFont(size=12),
            text_color=("gray80", "gray80")
        )
        version_label.place(relx=0.95, rely=0.15, anchor="ne")
    
    def create_config_section(self):
        """创建配置区域"""
        config_frame = ctk.CTkFrame(
            self.main_frame,
            corner_radius=18,
            border_width=2,
            border_color=("gray85", "gray25")
        )
        config_frame.pack(fill="x", pady=(0, 25))

        # 标题区域
        title_container = ctk.CTkFrame(config_frame, fg_color="transparent")
        title_container.pack(fill="x", padx=25, pady=(25, 20))

        config_title = ctk.CTkLabel(
            title_container,
            text="⚙️ 系统配置",
            font=ctk.CTkFont(size=22, weight="bold")
        )
        config_title.pack(side="left")

        # 添加配置状态指示器
        self.config_status = ctk.CTkLabel(
            title_container,
            text="🔴 未配置",
            font=ctk.CTkFont(size=14),
            text_color=("red", "lightcoral")
        )
        self.config_status.pack(side="right")

        # API密钥配置
        api_frame = ctk.CTkFrame(config_frame, fg_color="transparent")
        api_frame.pack(fill="x", padx=30, pady=(0, 20))

        api_label = ctk.CTkLabel(
            api_frame,
            text="🔑 API 密钥:",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        api_label.pack(anchor="w", pady=(0, 10))

        # API密钥输入框和显示切换
        api_input_frame = ctk.CTkFrame(api_frame, fg_color="transparent")
        api_input_frame.pack(fill="x", pady=(0, 15))
        api_input_frame.grid_columnconfigure(0, weight=1)

        self.api_key_entry = ctk.CTkEntry(
            api_input_frame,
            placeholder_text="请输入您的 Grok API 密钥 (xai-...)",
            show="*",
            font=ctk.CTkFont(size=14),
            height=50,
            border_width=2,
            corner_radius=12
        )
        self.api_key_entry.grid(row=0, column=0, sticky="ew", padx=(0, 12))
        self.api_key_entry.bind("<KeyRelease>", self.on_api_key_change)

        # 显示/隐藏密钥按钮
        self.show_key_var = tk.BooleanVar(value=False)
        self.show_key_btn = ctk.CTkButton(
            api_input_frame,
            text="👁️",
            command=self.toggle_api_key_visibility,
            width=50,
            height=50,
            corner_radius=12,
            hover=True
        )
        self.show_key_btn.grid(row=0, column=1, padx=(0, 8))

        # 测试API按钮
        self.test_api_btn = ctk.CTkButton(
            api_input_frame,
            text="🧪 测试",
            command=self.test_api_connection,
            width=80,
            height=50,
            corner_radius=12,
            hover=True,
            fg_color=("orange", "darkorange"),
            hover_color=("darkorange", "#cc5500")
        )
        self.test_api_btn.grid(row=0, column=2)

        # 系统提示词配置
        prompt_label = ctk.CTkLabel(
            api_frame,
            text="💬 系统提示词:",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        prompt_label.pack(anchor="w", pady=(0, 10))

        self.prompt_text = ctk.CTkTextbox(
            api_frame,
            height=130,
            font=ctk.CTkFont(size=14),
            border_width=2,
            corner_radius=12,
            wrap="word"
        )
        self.prompt_text.pack(fill="x", pady=(0, 25))
    
    def toggle_api_key_visibility(self):
        """切换API密钥显示/隐藏"""
        if self.api_key_entry.cget("show") == "*":
            self.api_key_entry.configure(show="")
            self.show_key_btn.configure(text="🙈")
        else:
            self.api_key_entry.configure(show="*")
            self.show_key_btn.configure(text="👁️")

    def on_api_key_change(self, event=None):
        """API密钥输入变化时的处理"""
        api_key = self.api_key_entry.get().strip()
        if api_key:
            if api_key.startswith("xai-"):
                self.config_status.configure(
                    text="🟡 已配置密钥",
                    text_color=("orange", "yellow")
                )
            else:
                self.config_status.configure(
                    text="🔴 密钥格式错误",
                    text_color=("red", "lightcoral")
                )
        else:
            self.config_status.configure(
                text="🔴 未配置",
                text_color=("red", "lightcoral")
            )

    def test_api_connection(self):
        """测试API连接"""
        api_key = self.api_key_entry.get().strip()
        if not api_key:
            self.log_message("请先输入API密钥", "ERROR")
            return

        self.test_api_btn.configure(state="disabled", text="🔄 测试中...")

        def test_thread():
            try:
                headers = {
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                }

                # 发送简单的测试请求
                test_data = {
                    "model": "grok-2-vision-1212",
                    "messages": [{"role": "user", "content": "Hello"}],
                    "max_tokens": 10
                }

                response = requests.post(self.api_url, headers=headers, json=test_data, timeout=10)

                if response.status_code == 200:
                    self.log_message("API连接测试成功", "SUCCESS")
                    self.config_status.configure(
                        text="🟢 连接正常",
                        text_color=("green", "lightgreen")
                    )
                else:
                    self.log_message(f"API测试失败: {response.status_code}", "ERROR")
                    self.config_status.configure(
                        text="🔴 连接失败",
                        text_color=("red", "lightcoral")
                    )
            except Exception as e:
                self.log_message(f"API测试异常: {str(e)}", "ERROR")
                self.config_status.configure(
                    text="🔴 连接异常",
                    text_color=("red", "lightcoral")
                )
            finally:
                self.test_api_btn.configure(state="normal", text="🧪 测试")

        threading.Thread(target=test_thread, daemon=True).start()


    
    def create_mode_section(self):
        """创建模式选择区域 - 使用TAB"""
        # 创建TAB控件
        self.mode_tabview = ctk.CTkTabview(
            self.main_frame,
            corner_radius=15,
            border_width=2,
            border_color=("gray85", "gray25"),
            segmented_button_fg_color=("gray90", "gray20"),
            segmented_button_selected_color=("blue", "darkblue"),
            segmented_button_selected_hover_color=("darkblue", "blue")
        )
        self.mode_tabview.pack(fill="x", pady=(0, 25))

        # 添加标签页
        self.mode_tabview.add("🖼️ 单例模式")
        self.mode_tabview.add("📁 批量模式")

        # 设置默认选中
        self.mode_tabview.set("🖼️ 单例模式")

        # 绑定切换事件
        self.mode_tabview.configure(command=self.on_tab_change)
    
    def create_work_section(self):
        """创建工作区域 - 在TAB中显示内容"""
        # 单例模式TAB内容
        single_tab = self.mode_tabview.tab("🖼️ 单例模式")

        # 图片选择区域
        images_container = ctk.CTkFrame(single_tab, fg_color="transparent")
        images_container.pack(fill="x", padx=20, pady=20)

        # 配置网格
        images_container.grid_columnconfigure(0, weight=1)
        images_container.grid_columnconfigure(1, weight=1)

        # 图片A区域
        self.image_a_frame = ctk.CTkFrame(
            images_container,
            corner_radius=15,
            border_width=2,
            border_color=("blue", "lightblue")
        )
        self.image_a_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 10))

        a_title = ctk.CTkLabel(
            self.image_a_frame,
            text="📷 图片 A",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=("blue", "lightblue")
        )
        a_title.pack(pady=(15, 10))

        # 图片A选择按钮
        self.select_a_btn = ctk.CTkButton(
            self.image_a_frame,
            text="🔍 选择图片A",
            command=self.browse_image_a,
            font=ctk.CTkFont(size=14),
            height=40,
            corner_radius=10,
            hover=True,
            fg_color=("blue", "#1e40af"),
            hover_color=("darkblue", "#1e3a8a")
        )
        self.select_a_btn.pack(padx=20, pady=(0, 15), fill="x")

        # 图片A预览区域
        self.image_a_preview_frame = ctk.CTkFrame(
            self.image_a_frame,
            fg_color=("gray95", "gray15"),
            corner_radius=10,
            border_width=2,
            border_color=("gray70", "gray40")
        )
        self.image_a_preview_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # 设置拖拽区域
        self.setup_drag_drop_area(self.image_a_preview_frame, "A")

        self.image_a_preview = ctk.CTkLabel(
            self.image_a_preview_frame,
            text="🖼️ 拖拽图片到此处\n或点击上方按钮选择\n\n支持格式: PNG, JPG, JPEG, BMP, GIF",
            font=ctk.CTkFont(size=12),
            text_color=("gray50", "gray60"),
            justify="center"
        )
        self.image_a_preview.pack(expand=True, pady=30)

        # 图片B区域
        self.image_b_frame = ctk.CTkFrame(
            images_container,
            corner_radius=15,
            border_width=2,
            border_color=("green", "lightgreen")
        )
        self.image_b_frame.grid(row=0, column=1, sticky="nsew", padx=(10, 0))

        b_title = ctk.CTkLabel(
            self.image_b_frame,
            text="📷 图片 B",
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=("green", "lightgreen")
        )
        b_title.pack(pady=(15, 10))

        # 图片B选择按钮
        self.select_b_btn = ctk.CTkButton(
            self.image_b_frame,
            text="🔍 选择图片B",
            command=self.browse_image_b,
            font=ctk.CTkFont(size=14),
            height=40,
            corner_radius=10,
            hover=True,
            fg_color=("green", "#16a34a"),
            hover_color=("darkgreen", "#15803d")
        )
        self.select_b_btn.pack(padx=20, pady=(0, 15), fill="x")

        # 图片B预览区域
        self.image_b_preview_frame = ctk.CTkFrame(
            self.image_b_frame,
            fg_color=("gray95", "gray15"),
            corner_radius=10,
            border_width=2,
            border_color=("gray70", "gray40")
        )
        self.image_b_preview_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # 设置拖拽区域
        self.setup_drag_drop_area(self.image_b_preview_frame, "B")

        self.image_b_preview = ctk.CTkLabel(
            self.image_b_preview_frame,
            text="🖼️ 拖拽图片到此处\n或点击上方按钮选择\n\n支持格式: PNG, JPG, JPEG, BMP, GIF",
            font=ctk.CTkFont(size=12),
            text_color=("gray50", "gray60"),
            justify="center"
        )
        self.image_b_preview.pack(expand=True, pady=30)

        # 批量模式TAB内容
        batch_tab = self.mode_tabview.tab("📁 批量模式")

        # 目录选择
        dirs_container = ctk.CTkFrame(batch_tab, fg_color="transparent")
        dirs_container.pack(fill="x", padx=20, pady=20)
        
        # 目录A
        folder_a_label = ctk.CTkLabel(
            dirs_container,
            text="📂 图片A目录:",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        folder_a_label.pack(anchor="w", pady=(0, 8))

        folder_a_frame = ctk.CTkFrame(dirs_container, fg_color="transparent")
        folder_a_frame.pack(fill="x", pady=(0, 20))

        self.folder_a_entry = ctk.CTkEntry(
            folder_a_frame,
            placeholder_text="选择包含图片A的目录",
            font=ctk.CTkFont(size=14),
            height=45,
            border_width=2,
            corner_radius=8
        )
        self.folder_a_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))

        self.browse_a_btn = ctk.CTkButton(
            folder_a_frame,
            text="🔍 浏览",
            command=self.browse_folder_a,
            width=120,
            height=45,
            corner_radius=8,
            hover=True
        )
        self.browse_a_btn.pack(side="right")

        # 目录B
        folder_b_label = ctk.CTkLabel(
            dirs_container,
            text="📂 图片B目录:",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        folder_b_label.pack(anchor="w", pady=(0, 8))

        folder_b_frame = ctk.CTkFrame(dirs_container, fg_color="transparent")
        folder_b_frame.pack(fill="x", pady=(0, 20))

        self.folder_b_entry = ctk.CTkEntry(
            folder_b_frame,
            placeholder_text="选择包含图片B的目录",
            font=ctk.CTkFont(size=14),
            height=45,
            border_width=2,
            corner_radius=8
        )
        self.folder_b_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))

        self.browse_b_btn = ctk.CTkButton(
            folder_b_frame,
            text="🔍 浏览",
            command=self.browse_folder_b,
            width=120,
            height=45,
            corner_radius=8,
            hover=True
        )
        self.browse_b_btn.pack(side="right")

        # 批量处理说明
        batch_info_card = ctk.CTkFrame(dirs_container, corner_radius=10, fg_color=("lightblue", "#1e3a5f"))
        batch_info_card.pack(fill="x", pady=(10, 5))

        batch_info = ctk.CTkLabel(
            batch_info_card,
            text="💡 提示：批量模式将自动匹配同名文件进行对比，结果保存为txt文件到B目录",
            font=ctk.CTkFont(size=14),
            text_color=("blue", "lightblue"),
            wraplength=800
        )
        batch_info.pack(anchor="w", padx=15, pady=15)
    
    def create_action_section(self):
        """创建操作按钮区域"""
        action_frame = ctk.CTkFrame(
            self.main_frame,
            fg_color="transparent"
        )
        action_frame.pack(fill="x", pady=(25, 35))

        # 主按钮容器
        main_button_container = ctk.CTkFrame(action_frame, fg_color="transparent")
        main_button_container.pack(pady=(0, 20))

        # 主要操作按钮
        self.generate_button = ctk.CTkButton(
            main_button_container,
            text="🚀 开始智能对比分析",
            command=self.generate_comparison,
            font=ctk.CTkFont(size=18, weight="bold"),
            height=60,
            width=350,
            corner_radius=30,
            hover=True,
            fg_color=("#667eea", "#1e40af"),
            hover_color=("#5a67d8", "#1e3a8a"),
            text_color=("white", "white")
        )
        self.generate_button.pack(pady=(0, 20))

        # 辅助按钮容器
        aux_buttons_frame = ctk.CTkFrame(main_button_container, fg_color="transparent")
        aux_buttons_frame.pack(pady=(0, 20))

        # 清空按钮
        self.clear_button = ctk.CTkButton(
            aux_buttons_frame,
            text="🗑️ 清空",
            command=self.clear_all,
            font=ctk.CTkFont(size=14),
            height=40,
            width=100,
            corner_radius=12,
            hover=True,
            fg_color=("gray", "gray40"),
            hover_color=("darkgray", "gray50")
        )
        self.clear_button.pack(side="left", padx=(0, 15))

        # 保存配置按钮
        self.save_config_button = ctk.CTkButton(
            aux_buttons_frame,
            text="💾 保存配置",
            command=self.save_config_from_ui,
            font=ctk.CTkFont(size=14),
            height=40,
            width=100,
            corner_radius=12,
            hover=True,
            fg_color=("orange", "darkorange"),
            hover_color=("darkorange", "#cc5500")
        )
        self.save_config_button.pack(side="left", padx=(0, 15))

        # 导出结果按钮
        self.export_button = ctk.CTkButton(
            aux_buttons_frame,
            text="📤 导出结果",
            command=self.export_results,
            font=ctk.CTkFont(size=14),
            height=40,
            width=100,
            corner_radius=12,
            hover=True,
            fg_color=("purple", "mediumpurple"),
            hover_color=("darkviolet", "purple"),
            state="disabled"
        )
        self.export_button.pack(side="left")

        # 状态显示卡片
        status_card = ctk.CTkFrame(
            action_frame,
            corner_radius=15,
            height=50,
            fg_color=("gray95", "gray20"),
            border_width=2,
            border_color=("gray80", "gray30")
        )
        status_card.pack(fill="x", padx=80)

        self.status_label = ctk.CTkLabel(
            status_card,
            text="🟢 系统就绪 - 请配置API密钥并选择图片开始分析",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=("green", "lightgreen")
        )
        self.status_label.pack(pady=12)
    
    def create_info_section(self):
        """创建信息显示区域"""
        info_frame = ctk.CTkFrame(self.main_frame, corner_radius=15)
        info_frame.pack(fill="both", expand=True, pady=(0, 20))
        
        # 标题
        info_title = ctk.CTkLabel(
            info_frame,
            text="📊 执行信息",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        info_title.pack(pady=(20, 15))

        # 创建标签页
        self.tabview = ctk.CTkTabview(info_frame, corner_radius=8, segmented_button_fg_color=("gray90", "gray20"))
        self.tabview.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # 执行日志标签页
        self.tabview.add("📋 执行日志")
        log_tab = self.tabview.tab("📋 执行日志")

        self.log_text = ctk.CTkTextbox(
            log_tab,
            font=ctk.CTkFont(size=12),
            wrap="word",
            corner_radius=8,
            border_width=2,
            border_color=("gray80", "gray30")
        )
        self.log_text.pack(fill="both", expand=True, padx=10, pady=10)

        # 对比结果标签页
        self.tabview.add("📊 对比结果")
        result_tab = self.tabview.tab("📊 对比结果")

        self.result_text = ctk.CTkTextbox(
            result_tab,
            font=ctk.CTkFont(size=13),
            wrap="word",
            corner_radius=8,
            border_width=2,
            border_color=("gray80", "gray30")
        )
        self.result_text.pack(fill="both", expand=True, padx=10, pady=10)
    
    def setup_drag_drop_area(self, frame, image_type):
        """设置拖拽区域"""
        # 绑定拖拽事件
        frame.bind("<Button-1>", lambda e: self.browse_image_a() if image_type == "A" else self.browse_image_b())

        # 添加悬停效果
        def on_enter(event):
            frame.configure(border_color=("blue", "lightblue") if image_type == "A" else ("green", "lightgreen"))
            frame.configure(fg_color=("gray90", "gray25"))

        def on_leave(event):
            frame.configure(border_color=("gray70", "gray40"))
            frame.configure(fg_color=("gray95", "gray15"))

        frame.bind("<Enter>", on_enter)
        frame.bind("<Leave>", on_leave)

    def on_tab_change(self):
        """TAB切换处理"""
        current_tab = self.mode_tabview.get()
        if current_tab == "🖼️ 单例模式":
            self.status_label.configure(text="🟢 单例模式 - 请选择两张图片进行对比", text_color=("green", "lightgreen"))
        else:
            self.status_label.configure(text="🟢 批量模式 - 请选择两个包含图片的目录", text_color=("green", "lightgreen"))
    
    def browse_image_a(self):
        """选择图片A"""
        file_path = filedialog.askopenfilename(
            title="选择图片A",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.bmp *.gif"), ("所有文件", "*.*")]
        )
        if file_path:
            self.image_a_path = file_path
            self.update_image_preview(file_path, self.image_a_preview, "A")
    
    def browse_image_b(self):
        """选择图片B"""
        file_path = filedialog.askopenfilename(
            title="选择图片B",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.bmp *.gif"), ("所有文件", "*.*")]
        )
        if file_path:
            self.image_b_path = file_path
            self.update_image_preview(file_path, self.image_b_preview, "B")
    
    def browse_folder_a(self):
        """选择目录A"""
        folder_path = filedialog.askdirectory(title="选择图片A目录")
        if folder_path:
            self.folder_a_entry.delete(0, tk.END)
            self.folder_a_entry.insert(0, folder_path)
            self.config["batch_mode"]["folder_a"] = folder_path
            self.save_config()
    
    def browse_folder_b(self):
        """选择目录B"""
        folder_path = filedialog.askdirectory(title="选择图片B目录")
        if folder_path:
            self.folder_b_entry.delete(0, tk.END)
            self.folder_b_entry.insert(0, folder_path)
            self.config["batch_mode"]["folder_b"] = folder_path
            self.save_config()
    
    def update_image_preview(self, image_path, preview_label, image_type):
        """更新图片预览"""
        try:
            # 检查文件大小
            file_size_bytes = os.path.getsize(image_path)
            file_size_mb = file_size_bytes / (1024 * 1024)

            if file_size_mb > 10:
                raise Exception(f"文件过大 ({file_size_mb:.1f}MB)，请选择小于10MB的图片")

            # 加载图片
            image = Image.open(image_path)

            # 调整图片大小用于预览，保持宽高比
            display_image = image.copy()
            display_image.thumbnail((280, 280), Image.Resampling.LANCZOS)

            # 转换为PhotoImage
            photo = ImageTk.PhotoImage(display_image)

            # 获取图片信息
            file_name = os.path.basename(image_path)
            file_size_kb = file_size_bytes // 1024
            image_size = f"{image.width}×{image.height}"

            # 获取图片格式和色彩模式
            image_format = image.format or "Unknown"
            color_mode = image.mode

            # 创建详细信息文本
            info_text = f"✅ {file_name}\n📏 {image_size} | 📁 {file_size_kb} KB\n🎨 {image_format} | {color_mode}"

            preview_label.configure(image=photo, text=info_text, compound="top")
            preview_label.image = photo  # 保持引用

            # 更新按钮文本和样式
            if image_type == "A":
                self.select_a_btn.configure(
                    text=f"✅ 图片A已就绪",
                    fg_color=("green", "#16a34a"),
                    hover_color=("darkgreen", "#15803d")
                )
                self.image_a_preview_frame.configure(
                    border_color=("green", "lightgreen"),
                    fg_color=("lightgreen", "darkgreen")
                )
            else:
                self.select_b_btn.configure(
                    text=f"✅ 图片B已就绪",
                    fg_color=("green", "#16a34a"),
                    hover_color=("darkgreen", "#15803d")
                )
                self.image_b_preview_frame.configure(
                    border_color=("green", "lightgreen"),
                    fg_color=("lightgreen", "darkgreen")
                )

            # 检查是否两张图片都已选择
            if hasattr(self, 'image_a_path') and hasattr(self, 'image_b_path') and \
               self.image_a_path and self.image_b_path:
                self.status_label.configure(
                    text="🟢 图片已就绪 - 可以开始对比分析",
                    text_color=("green", "lightgreen")
                )

            self.log_message(f"图片{image_type}加载成功: {file_name}", "SUCCESS")

        except Exception as e:
            # 显示错误信息
            error_message = f"❌ 加载失败\n{str(e)[:50]}..."
            preview_label.configure(image=None, text=error_message)

            # 更新按钮状态
            if image_type == "A":
                self.select_a_btn.configure(
                    text="❌ 重新选择图片A",
                    fg_color=("red", "#dc2626"),
                    hover_color=("darkred", "#b91c1c")
                )
                self.image_a_preview_frame.configure(
                    border_color=("red", "lightcoral"),
                    fg_color=("lightcoral", "darkred")
                )
                self.image_a_path = None
            else:
                self.select_b_btn.configure(
                    text="❌ 重新选择图片B",
                    fg_color=("red", "#dc2626"),
                    hover_color=("darkred", "#b91c1c")
                )
                self.image_b_preview_frame.configure(
                    border_color=("red", "lightcoral"),
                    fg_color=("lightcoral", "darkred")
                )
                self.image_b_path = None

            self.log_message(f"图片{image_type}加载失败: {str(e)}", "ERROR")
    
    def clear_all(self):
        """清空所有内容"""
        # 清空图片选择
        self.image_a_path = None
        self.image_b_path = None

        # 重置预览
        self.image_a_preview.configure(
            image=None,
            text="🖼️ 拖拽图片到此处\n或点击上方按钮选择\n\n支持格式: PNG, JPG, JPEG, BMP, GIF\n最大尺寸: 10MB"
        )
        self.image_b_preview.configure(
            image=None,
            text="🖼️ 拖拽图片到此处\n或点击上方按钮选择\n\n支持格式: PNG, JPG, JPEG, BMP, GIF\n最大尺寸: 10MB"
        )

        # 重置按钮
        self.select_a_btn.configure(
            text="🔍 选择图片A (Ctrl+O)",
            fg_color=("blue", "#1e40af"),
            hover_color=("darkblue", "#1e3a8a")
        )
        self.select_b_btn.configure(
            text="🔍 选择图片B (Ctrl+P)",
            fg_color=("green", "#16a34a"),
            hover_color=("darkgreen", "#15803d")
        )

        # 重置边框颜色
        self.image_a_preview_frame.configure(border_color=("gray70", "gray40"))
        self.image_b_preview_frame.configure(border_color=("gray70", "gray40"))

        # 清空日志和结果
        self.log_text.delete(1.0, tk.END)
        self.result_text.delete(1.0, tk.END)

        # 重置状态
        self.status_label.configure(
            text="🟢 已清空 - 请重新选择图片",
            text_color=("green", "lightgreen")
        )

        # 禁用导出按钮
        self.export_button.configure(state="disabled")

        self.log_message("已清空所有内容", "INFO")

    def export_results(self):
        """导出结果"""
        result_content = self.result_text.get(1.0, tk.END).strip()
        if not result_content:
            self.log_message("没有可导出的结果", "WARNING")
            return

        # 选择保存位置
        file_path = filedialog.asksaveasfilename(
            title="导出对比结果",
            defaultextension=".txt",
            filetypes=[
                ("文本文件", "*.txt"),
                ("Markdown文件", "*.md"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"图像对比分析结果\n")
                    f.write(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"{'='*50}\n\n")
                    f.write(result_content)

                self.log_message(f"结果已导出到: {file_path}", "SUCCESS")
                self.status_label.configure(
                    text="✅ 结果导出成功",
                    text_color=("green", "lightgreen")
                )
            except Exception as e:
                self.log_message(f"导出失败: {str(e)}", "ERROR")

    def load_saved_config(self):
        """加载保存的配置"""
        self.api_key_entry.delete(0, tk.END)
        self.api_key_entry.insert(0, self.config.get("api_key", ""))

        self.prompt_text.delete(1.0, tk.END)
        self.prompt_text.insert(1.0, self.config.get("system_prompt", ""))

        if self.config.get("batch_mode"):
            self.folder_a_entry.delete(0, tk.END)
            self.folder_a_entry.insert(0, self.config["batch_mode"].get("folder_a", ""))
            self.folder_b_entry.delete(0, tk.END)
            self.folder_b_entry.insert(0, self.config["batch_mode"].get("folder_b", ""))

        # 检查API密钥状态
        self.on_api_key_change()
    
    def save_config_from_ui(self):
        """从界面保存配置"""
        self.config["api_key"] = self.api_key_entry.get()
        self.config["system_prompt"] = self.prompt_text.get(1.0, tk.END).strip()
        if self.mode_var.get() == "batch":
            self.config["batch_mode"]["folder_a"] = self.folder_a_entry.get()
            self.config["batch_mode"]["folder_b"] = self.folder_b_entry.get()
        self.save_config()
    
    def log_message(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        # 图标映射
        level_icons = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "ERROR": "❌",
            "WARNING": "⚠️"
        }
        
        # 颜色映射
        level_colors = {
            "INFO": ("blue", "lightblue"),
            "SUCCESS": ("green", "lightgreen"),
            "ERROR": ("red", "lightcoral"),
            "WARNING": ("orange", "lightyellow")
        }
        
        icon = level_icons.get(level, "ℹ️")
        color = level_colors.get(level, ("gray", "lightgray"))
        
        # 创建带颜色的日志条目
        log_entry = f"[{timestamp}] {icon} {message}\n"
        
        # 添加到日志
        current_pos = self.log_text.index(tk.END)
        self.log_text.insert(tk.END, log_entry)
        
        # 设置标签颜色
        tag_name = f"log_{level}_{timestamp}"
        end_pos = self.log_text.index(tk.END + "-1c")
        self.log_text.tag_add(tag_name, current_pos, end_pos)
        self.log_text.tag_config(tag_name, foreground=color[0 if ctk.get_appearance_mode() == "Light" else 1])
        
        # 滚动到最新内容
        self.log_text.see(tk.END)
        
        # 更新界面
        self.root.update()
    
    def call_grok_api(self, image_a_path, image_b_path, prompt):
        """调用Grok API"""
        try:
            # 读取图片文件并编码为base64
            with open(image_a_path, 'rb') as f:
                image_a_data = base64.b64encode(f.read()).decode('utf-8')
            
            with open(image_b_path, 'rb') as f:
                image_b_data = base64.b64encode(f.read()).decode('utf-8')
            
            # 获取图片MIME类型
            def get_mime_type(file_path):
                ext = os.path.splitext(file_path)[1].lower()
                mime_types = {
                    '.png': 'image/png',
                    '.jpg': 'image/jpeg',
                    '.jpeg': 'image/jpeg',
                    '.bmp': 'image/bmp',
                    '.gif': 'image/gif'
                }
                return mime_types.get(ext, 'image/png')
            
            mime_a = get_mime_type(image_a_path)
            mime_b = get_mime_type(image_b_path)
            
            # 准备请求数据
            headers = {
                "Authorization": f"Bearer {self.config['api_key']}",
                "Content-Type": "application/json"
            }
            
            # 构建消息
            messages = [
                {
                    "role": "system",
                    "content": prompt
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请对比分析这两张图片："
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:{mime_a};base64,{image_a_data}"
                            }
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:{mime_b};base64,{image_b_data}"
                            }
                        }
                    ]
                }
            ]
            
            data = {
                "model": "grok-2-vision-1212",
                "messages": messages,
                "max_tokens": 1000
            }
            
            # 发送请求
            self.log_message("正在发送请求到Grok API...", "INFO")
            self.log_message(f"使用模型: {data['model']}", "INFO")
            self.log_message(f"图片A大小: {len(image_a_data)} 字符", "INFO")
            self.log_message(f"图片B大小: {len(image_b_data)} 字符", "INFO")
            
            response = requests.post(self.api_url, headers=headers, json=data, timeout=60)
            
            self.log_message(f"API响应状态码: {response.status_code}", "INFO")
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    if "message" in result["choices"][0] and "content" in result["choices"][0]["message"]:
                        content = result["choices"][0]["message"]["content"]
                        self.log_message(f"API调用成功，返回内容长度: {len(content)} 字符", "SUCCESS")
                        return content
                    else:
                        self.log_message("API响应格式异常", "ERROR")
                        return f"API响应格式异常: {result}"
                else:
                    self.log_message("API响应格式异常", "ERROR")
                    return f"API响应格式异常: {result}"
            else:
                error_detail = response.text
                self.log_message(f"API调用失败: {response.status_code}", "ERROR")
                return f"API调用失败: {response.status_code} - {error_detail}"
                
        except Exception as e:
            self.log_message(f"API调用异常: {str(e)}", "ERROR")
            return f"API调用异常: {str(e)}"
    
    def generate_comparison(self):
        """生成对比结果"""
        try:
            # 保存配置
            self.save_config_from_ui()
            
            # 检查API密钥
            if not self.config["api_key"]:
                self.log_message("请先配置API密钥", "ERROR")
                self.status_label.configure(
                    text="❌ 错误 - 请先配置API密钥", text_color="red"
                )
                return
            
            # 检查提示词
            if not self.config["system_prompt"]:
                self.log_message("请输入系统提示词", "ERROR")
                self.status_label.configure(
                    text="❌ 错误 - 请输入系统提示词", text_color="red"
                )
                return
            
            # 更新按钮和状态
            self.generate_button.configure(state="disabled", text="🔄 处理中...")
            self.status_label.configure(text="🔄 正在准备...", text_color="orange")
            
            # 清空日志和结果
            self.log_text.delete(1.0, tk.END)
            self.result_text.delete(1.0, tk.END)
            
            # 根据当前TAB判断模式
            current_tab = self.mode_tabview.get()
            if current_tab == "🖼️ 单例模式":
                # 单例模式
                self.status_label.configure(text="🔄 正在处理单例模式...", text_color=("orange", "yellow"))
                threading.Thread(target=self.single_mode_process, daemon=True).start()
            else:
                # 批量模式
                self.status_label.configure(text="🔄 正在处理批量模式...", text_color=("orange", "yellow"))
                threading.Thread(target=self.batch_mode_process, daemon=True).start()
        except Exception as e:
            self.log_message(f"处理异常: {str(e)}", "ERROR")
            self.status_label.configure(text="❌ 处理异常", text_color="red")
        finally:
            self.generate_button.configure(state="normal", text="🚀 开始智能对比分析")
    
    def single_mode_process(self):
        """单例模式处理"""
        try:
            if not hasattr(self, 'image_a_path') or not hasattr(self, 'image_b_path'):
                self.log_message("请选择两张图片", "ERROR")
                self.status_label.configure(text="❌ 错误 - 请选择两张图片", text_color="red")
                return
            
            if not os.path.exists(self.image_a_path):
                self.log_message(f"图片A不存在: {self.image_a_path}", "ERROR")
                self.status_label.configure(text="❌ 错误 - 图片A不存在", text_color="red")
                return
            
            if not os.path.exists(self.image_b_path):
                self.log_message(f"图片B不存在: {self.image_b_path}", "ERROR")
                self.status_label.configure(text="❌ 错误 - 图片B不存在", text_color="red")
                return
            
            self.log_message("开始处理单例模式", "INFO")
            self.log_message(f"图片A: {os.path.basename(self.image_a_path)}", "INFO")
            self.log_message(f"图片B: {os.path.basename(self.image_b_path)}", "INFO")
            self.status_label.configure(text="🔄 正在调用API...", text_color="orange")
            
            # 调用API
            result = self.call_grok_api(self.image_a_path, self.image_b_path, self.config["system_prompt"])
            
            if result.startswith("API调用失败") or result.startswith("API调用异常"):
                self.log_message("API调用失败", "ERROR")
                self.status_label.configure(text="❌ 处理失败", text_color="red")
            else:
                self.log_message("单例模式处理完成", "SUCCESS")
                self.result_text.insert(tk.END, result)
                self.status_label.configure(text="✅ 对比分析完成", text_color=("green", "lightgreen"))
                # 启用导出按钮
                self.export_button.configure(state="normal")
                # 自动切换到结果标签页
                self.tabview.set("📊 对比结果")
                # 添加完成提示
                self.log_message(f"分析结果已生成，共 {len(result)} 字符", "SUCCESS")
            
        except Exception as e:
            self.log_message(f"处理异常: {str(e)}", "ERROR")
            self.status_label.configure(text="❌ 处理异常", text_color="red")
        finally:
            self.generate_button.configure(state="normal", text="🚀 开始智能对比分析")
    
    def batch_mode_process(self):
        """批量模式处理"""
        try:
            folder_a = self.folder_a_entry.get().strip()
            folder_b = self.folder_b_entry.get().strip()
            
            if not folder_a or not folder_b:
                self.log_message("请选择两个目录", "ERROR")
                self.status_label.configure(text="❌ 错误 - 请选择两个目录", text_color="red")
                return
            
            if not os.path.exists(folder_a):
                self.log_message(f"目录A不存在: {folder_a}", "ERROR")
                self.status_label.configure(text="❌ 错误 - 目录A不存在", text_color="red")
                return
            
            if not os.path.exists(folder_b):
                self.log_message(f"目录B不存在: {folder_b}", "ERROR")
                self.status_label.configure(text="❌ 错误 - 目录B不存在", text_color="red")
                return
            
            self.log_message("开始处理批量模式", "INFO")
            self.log_message(f"目录A: {folder_a}", "INFO")
            self.log_message(f"目录B: {folder_b}", "INFO")
            
            # 获取目录中的图片文件
            image_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.gif')
            
            files_a = [f for f in os.listdir(folder_a) if f.lower().endswith(image_extensions)]
            files_b = [f for f in os.listdir(folder_b) if f.lower().endswith(image_extensions)]
            
            if not files_a:
                self.log_message("目录A中没有找到图片文件", "ERROR")
                self.status_label.configure(text="❌ 错误 - 目录A中无图片", text_color="red")
                return
            
            if not files_b:
                self.log_message("目录B中没有找到图片文件", "ERROR")
                self.status_label.configure(text="❌ 错误 - 目录B中无图片", text_color="red")
                return
            
            # 按文件名排序
            files_a.sort()
            files_b.sort()
            
            self.log_message(f"目录A中找到 {len(files_a)} 个图片文件", "INFO")
            self.log_message(f"目录B中找到 {len(files_b)} 个图片文件", "INFO")
            
            # 处理匹配的文件
            processed_count = 0
            total_pairs = min(len(files_a), len(files_b))
            
            for i, (file_a, file_b) in enumerate(zip(files_a, files_b)):
                # 检查文件名是否匹配
                name_a = os.path.splitext(file_a)[0]
                name_b = os.path.splitext(file_b)[0]
                
                progress = f"({i+1}/{total_pairs})"
                self.status_label.configure(text=f"🔄 批量处理中 {progress}", text_color="orange")
                
                if name_a != name_b:
                    self.log_message(f"跳过: 文件名不匹配 {file_a} vs {file_b}", "WARNING")
                    continue
                
                image_a_path = os.path.join(folder_a, file_a)
                image_b_path = os.path.join(folder_b, file_b)
                
                self.log_message(f"处理第 {i+1} 组: {file_a} vs {file_b}", "INFO")
                
                # 调用API
                result = self.call_grok_api(image_a_path, image_b_path, self.config["system_prompt"])
                
                if result.startswith("API调用失败") or result.startswith("API调用异常"):
                    self.log_message("API调用失败，批量处理已停止", "ERROR")
                    self.status_label.configure(text="❌ 批量处理失败", text_color="red")
                    break
                else:
                    # 保存结果到文件
                    output_file = os.path.join(folder_b, f"{name_a}.txt")
                    try:
                        with open(output_file, 'w', encoding='utf-8') as f:
                            f.write(result)
                        self.log_message(f"成功: 结果已保存到 {os.path.basename(output_file)}", "SUCCESS")
                        processed_count += 1
                    except Exception as e:
                        self.log_message(f"保存文件失败: {str(e)}", "ERROR")
                        self.status_label.configure(text="❌ 保存失败", text_color="red")
                        break
            
            self.log_message(f"批量处理完成，共处理 {processed_count} 组图片", "SUCCESS")
            self.status_label.configure(text=f"✅ 批量处理完成 ({processed_count}组)", text_color="green")
            
        except Exception as e:
            self.log_message(f"批量处理异常: {str(e)}", "ERROR")
            self.status_label.configure(text="❌ 批量处理异常", text_color="red")
        finally:
            self.generate_button.configure(state="normal", text="🚀 开始智能对比分析")
    
    def on_closing(self):
        """窗口关闭事件处理"""
        self.root.destroy()
    
    def run(self):
        """运行应用"""
        self.root.mainloop()

def main():
    """主函数"""
    app = ModernImageComparisonTool()
    app.run()

if __name__ == "__main__":
    main() 

