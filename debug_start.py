#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试启动脚本 - 用于诊断启动问题
"""

import sys
import traceback
import os

def check_environment():
    """检查运行环境"""
    print("🔍 环境检查:")
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print(f"脚本路径: {__file__}")
    print()

def check_imports():
    """检查导入"""
    print("🔍 导入检查:")
    
    modules = [
        ("tkinter", "Tkinter基础库"),
        ("customtkinter", "CustomTkinter现代UI"),
        ("requests", "HTTP请求库"),
        ("PIL", "Pillow图像处理"),
        ("json", "JSON处理"),
        ("threading", "多线程支持"),
        ("datetime", "日期时间"),
        ("base64", "Base64编码"),
        ("os", "操作系统接口")
    ]
    
    failed_modules = []
    
    for module_name, description in modules:
        try:
            __import__(module_name)
            print(f"✅ {module_name} - {description}")
        except ImportError as e:
            print(f"❌ {module_name} - {description} (错误: {e})")
            failed_modules.append(module_name)
    
    print()
    return failed_modules

def test_customtkinter():
    """测试CustomTkinter"""
    print("🔍 CustomTkinter测试:")
    
    try:
        import customtkinter as ctk
        
        # 设置基本配置
        ctk.set_appearance_mode("system")
        ctk.set_default_color_theme("blue")
        
        # 创建测试窗口
        root = ctk.CTk()
        root.title("测试窗口")
        root.geometry("400x300")
        
        # 添加测试组件
        label = ctk.CTkLabel(root, text="CustomTkinter测试成功！")
        label.pack(pady=20)
        
        button = ctk.CTkButton(root, text="关闭", command=root.destroy)
        button.pack(pady=10)
        
        print("✅ CustomTkinter窗口创建成功")
        
        # 显示窗口1秒后关闭
        root.after(1000, root.destroy)
        root.mainloop()
        
        print("✅ CustomTkinter测试完成")
        return True
        
    except Exception as e:
        print(f"❌ CustomTkinter测试失败: {e}")
        traceback.print_exc()
        return False

def test_main_import():
    """测试主程序导入"""
    print("🔍 主程序导入测试:")
    
    try:
        # 检查文件是否存在
        if not os.path.exists("image_comparison_tool_modern.py"):
            print("❌ 主程序文件不存在")
            return False
        
        print("✅ 主程序文件存在")
        
        # 尝试编译检查语法
        import py_compile
        py_compile.compile("image_comparison_tool_modern.py", doraise=True)
        print("✅ 主程序语法检查通过")
        
        # 尝试导入主程序模块
        sys.path.insert(0, os.getcwd())
        
        # 先导入必要的模块
        import customtkinter as ctk
        import tkinter as tk
        
        # 设置CTk
        ctk.set_appearance_mode("system")
        ctk.set_default_color_theme("blue")
        
        print("✅ 主程序导入准备完成")
        return True
        
    except Exception as e:
        print(f"❌ 主程序导入失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 图像对比工具启动诊断")
    print("=" * 50)
    print()
    
    # 环境检查
    check_environment()
    
    # 导入检查
    failed_modules = check_imports()
    
    if failed_modules:
        print(f"❌ 缺少模块: {', '.join(failed_modules)}")
        print("请运行以下命令安装:")
        print("pip install customtkinter requests Pillow darkdetect")
        return False
    
    # CustomTkinter测试
    if not test_customtkinter():
        print("❌ CustomTkinter测试失败")
        return False
    
    # 主程序导入测试
    if not test_main_import():
        print("❌ 主程序导入测试失败")
        return False
    
    print("✅ 所有测试通过！")
    print()
    print("🚀 尝试启动主程序...")
    
    try:
        # 启动主程序
        import image_comparison_tool_modern
        app = image_comparison_tool_modern.ModernImageComparisonTool()
        app.run()
        
    except Exception as e:
        print(f"❌ 主程序启动失败: {e}")
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n按回车键退出...")
            input()
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        traceback.print_exc()
        print("\n按回车键退出...")
        input()
        sys.exit(1)
