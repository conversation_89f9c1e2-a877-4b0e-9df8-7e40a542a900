#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试所有版本的启动情况
"""

import sys
import os
import traceback

def test_version(script_name, version_name):
    """测试特定版本"""
    print(f"\n🔍 测试 {version_name}...")
    
    if not os.path.exists(script_name):
        print(f"❌ 文件不存在: {script_name}")
        return False
    
    try:
        # 尝试编译检查语法
        import py_compile
        py_compile.compile(script_name, doraise=True)
        print(f"✅ {version_name} 语法检查通过")
        
        # 尝试导入模块
        module_name = script_name.replace('.py', '')
        spec = __import__(module_name)
        print(f"✅ {version_name} 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ {version_name} 测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("🔍 测试依赖包...")
    
    deps = [
        ("tkinter", "Tkinter基础库"),
        ("requests", "HTTP请求库"),
        ("PIL", "Pillow图像处理"),
        ("json", "JSON处理"),
        ("threading", "多线程"),
        ("base64", "Base64编码"),
        ("datetime", "日期时间"),
        ("os", "操作系统接口")
    ]
    
    failed = []
    for module, desc in deps:
        try:
            __import__(module)
            print(f"✅ {module} - {desc}")
        except ImportError:
            print(f"❌ {module} - {desc}")
            failed.append(module)
    
    # 测试CustomTkinter
    try:
        import customtkinter
        print("✅ customtkinter - 现代化UI框架")
    except ImportError:
        print("❌ customtkinter - 现代化UI框架")
        failed.append("customtkinter")
    
    return failed

def main():
    """主函数"""
    print("🚀 图像对比工具版本测试")
    print("=" * 50)
    
    # 测试Python版本
    print(f"Python版本: {sys.version}")
    if sys.version_info < (3, 8):
        print("⚠️  建议使用Python 3.8或更高版本")
    
    # 测试依赖包
    failed_deps = test_dependencies()
    
    if failed_deps:
        print(f"\n⚠️  缺少依赖包: {', '.join(failed_deps)}")
        print("请运行: pip install customtkinter requests Pillow darkdetect")
    
    # 测试各个版本
    versions = [
        ("image_comparison_tool.py", "原版界面"),
        ("image_comparison_tool_simple.py", "简化版现代化界面"),
        ("image_comparison_tool_modern.py", "完整版现代化界面"),
        ("smart_start.py", "智能启动脚本")
    ]
    
    results = {}
    for script, name in versions:
        results[name] = test_version(script, name)
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    working_versions = []
    for name, success in results.items():
        status = "✅ 可用" if success else "❌ 不可用"
        print(f"  {name}: {status}")
        if success:
            working_versions.append(name)
    
    print(f"\n🎯 推荐启动方式:")
    if "简化版现代化界面" in working_versions:
        print("  1. 简化版现代化界面 (推荐)")
        print("     python image_comparison_tool_simple.py")
    
    if "智能启动脚本" in working_versions:
        print("  2. 智能启动脚本")
        print("     python smart_start.py")
    
    if "原版界面" in working_versions:
        print("  3. 原版界面 (备用)")
        print("     python image_comparison_tool.py")
    
    if not working_versions:
        print("  ❌ 没有可用版本，请检查依赖包安装")
    
    print(f"\n📁 当前目录: {os.getcwd()}")
    print("💡 提示: 双击 start_enhanced.bat 可自动选择最佳版本")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"测试异常: {e}")
        traceback.print_exc()
    
    print("\n按回车键退出...")
    input()
