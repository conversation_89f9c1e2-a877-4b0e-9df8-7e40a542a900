#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版现代化图像对比工具
解决CustomTkinter兼容性问题
"""

import os
import sys

# 设置环境变量解决DPI问题
os.environ['CTK_DISABLE_DPI_AWARENESS'] = '1'

try:
    import customtkinter as ctk
    # 禁用DPI感知
    try:
        ctk.deactivate_automatic_dpi_awareness()
    except:
        pass

    # 设置简单模式
    ctk.set_appearance_mode("light")
    ctk.set_default_color_theme("blue")

except ImportError:
    print("CustomTkinter未安装，请运行: pip install customtkinter")
    sys.exit(1)

import tkinter as tk
from tkinter import filedialog, messagebox
import requests
import json
import threading
from PIL import Image, ImageTk
import base64
import datetime

class SimpleImageComparisonTool:
    def __init__(self):
        try:
            # 创建主窗口
            self.root = ctk.CTk()
            self.root.title("🖼️ 图像对比工具 - 简化版")
            self.root.geometry("1200x800")

            # 初始化变量
            self.image_a_path = None
            self.image_b_path = None
            self.config = self.load_config()
            self.api_url = "https://api.x.ai/v1/chat/completions"

            # 创建界面
            self.create_widgets()
            self.load_saved_config()

            # 设置关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        except Exception as e:
            print(f"初始化失败: {e}")
            # 回退到原版
            self.fallback_to_original()

    def fallback_to_original(self):
        """回退到原版"""
        try:
            import subprocess
            subprocess.run([sys.executable, "image_comparison_tool.py"])
        except:
            print("无法启动原版，请检查文件是否存在")
        sys.exit(1)

    def load_config(self):
        """加载配置"""
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            return {
                "api_key": "",
                "system_prompt": "请对比这两张图片，详细描述它们的差异和相似之处。"
            }

    def save_config(self):
        """保存配置"""
        with open("config.json", "w", encoding="utf-8") as f:
            json.dump(self.config, f, ensure_ascii=False, indent=4)

    def create_widgets(self):
        """创建界面"""
        # 主框架
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="🖼️ 图像对比分析工具",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 30))

        # 配置区域
        config_frame = ctk.CTkFrame(main_frame)
        config_frame.pack(fill="x", padx=20, pady=(0, 20))

        # API密钥
        ctk.CTkLabel(config_frame, text="API密钥:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=20, pady=(20, 5))
        self.api_key_entry = ctk.CTkEntry(config_frame, placeholder_text="输入Grok API密钥", show="*", width=400)
        self.api_key_entry.pack(padx=20, pady=(0, 10))

        # 提示词
        ctk.CTkLabel(config_frame, text="系统提示词:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=20, pady=(10, 5))
        self.prompt_text = ctk.CTkTextbox(config_frame, height=80, width=400)
        self.prompt_text.pack(padx=20, pady=(0, 20))

        # 图片选择区域
        images_frame = ctk.CTkFrame(main_frame)
        images_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        # 图片A
        img_a_frame = ctk.CTkFrame(images_frame)
        img_a_frame.pack(side="left", fill="both", expand=True, padx=(20, 10), pady=20)

        ctk.CTkLabel(img_a_frame, text="图片 A", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(20, 10))
        self.select_a_btn = ctk.CTkButton(img_a_frame, text="选择图片A", command=self.browse_image_a)
        self.select_a_btn.pack(pady=(0, 10))

        self.preview_a = ctk.CTkLabel(img_a_frame, text="未选择图片", width=200, height=150)
        self.preview_a.pack(pady=(0, 20))

        # 图片B
        img_b_frame = ctk.CTkFrame(images_frame)
        img_b_frame.pack(side="right", fill="both", expand=True, padx=(10, 20), pady=20)

        ctk.CTkLabel(img_b_frame, text="图片 B", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(20, 10))
        self.select_b_btn = ctk.CTkButton(img_b_frame, text="选择图片B", command=self.browse_image_b)
        self.select_b_btn.pack(pady=(0, 10))

        self.preview_b = ctk.CTkLabel(img_b_frame, text="未选择图片", width=200, height=150)
        self.preview_b.pack(pady=(0, 20))

        # 操作按钮
        self.analyze_btn = ctk.CTkButton(
            main_frame,
            text="🚀 开始分析",
            command=self.start_analysis,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            width=200
        )
        self.analyze_btn.pack(pady=20)

        # 状态标签
        self.status_label = ctk.CTkLabel(main_frame, text="就绪", font=ctk.CTkFont(size=14))
        self.status_label.pack(pady=(0, 10))

        # 结果区域
        result_frame = ctk.CTkFrame(main_frame)
        result_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))

        ctk.CTkLabel(result_frame, text="分析结果:", font=ctk.CTkFont(size=14, weight="bold")).pack(anchor="w", padx=20, pady=(20, 5))
        self.result_text = ctk.CTkTextbox(result_frame, height=150)
        self.result_text.pack(fill="both", expand=True, padx=20, pady=(0, 20))

    def browse_image_a(self):
        """选择图片A"""
        file_path = filedialog.askopenfilename(
            title="选择图片A",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.bmp *.gif")]
        )
        if file_path:
            self.image_a_path = file_path
            self.update_preview(file_path, self.preview_a, "A")

    def browse_image_b(self):
        """选择图片B"""
        file_path = filedialog.askopenfilename(
            title="选择图片B",
            filetypes=[("图片文件", "*.png *.jpg *.jpeg *.bmp *.gif")]
        )
        if file_path:
            self.image_b_path = file_path
            self.update_preview(file_path, self.preview_b, "B")

    def update_preview(self, image_path, preview_label, image_type):
        """更新预览"""
        try:
            image = Image.open(image_path)
            image.thumbnail((150, 150), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(image)

            file_name = os.path.basename(image_path)
            preview_label.configure(image=photo, text=f"✅ {file_name}")
            preview_label.image = photo

            if image_type == "A":
                self.select_a_btn.configure(text="✅ 图片A已选择", fg_color="green")
            else:
                self.select_b_btn.configure(text="✅ 图片B已选择", fg_color="green")

        except Exception as e:
            preview_label.configure(text=f"❌ 加载失败")
            if image_type == "A":
                self.select_a_btn.configure(text="❌ 重新选择A", fg_color="red")
            else:
                self.select_b_btn.configure(text="❌ 重新选择B", fg_color="red")

    def load_saved_config(self):
        """加载配置"""
        self.api_key_entry.insert(0, self.config.get("api_key", ""))
        self.prompt_text.insert("1.0", self.config.get("system_prompt", ""))

    def save_config_from_ui(self):
        """保存配置"""
        self.config["api_key"] = self.api_key_entry.get()
        self.config["system_prompt"] = self.prompt_text.get("1.0", tk.END).strip()
        self.save_config()

    def start_analysis(self):
        """开始分析"""
        # 保存配置
        self.save_config_from_ui()

        # 检查输入
        if not self.config["api_key"]:
            messagebox.showerror("错误", "请输入API密钥")
            return

        if not self.image_a_path or not self.image_b_path:
            messagebox.showerror("错误", "请选择两张图片")
            return

        # 禁用按钮
        self.analyze_btn.configure(state="disabled", text="🔄 分析中...")
        self.status_label.configure(text="正在分析...")

        # 启动分析线程
        threading.Thread(target=self.analyze_images, daemon=True).start()

    def analyze_images(self):
        """分析图片"""
        try:
            # 读取图片
            with open(self.image_a_path, 'rb') as f:
                image_a_data = base64.b64encode(f.read()).decode('utf-8')

            with open(self.image_b_path, 'rb') as f:
                image_b_data = base64.b64encode(f.read()).decode('utf-8')

            # 准备请求
            headers = {
                "Authorization": f"Bearer {self.config['api_key']}",
                "Content-Type": "application/json"
            }

            messages = [
                {"role": "system", "content": self.config["system_prompt"]},
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "请对比分析这两张图片："},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_a_data}"}},
                        {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_b_data}"}}
                    ]
                }
            ]

            data = {
                "model": "grok-2-vision-1212",
                "messages": messages,
                "max_tokens": 1000
            }

            # 发送请求
            response = requests.post(self.api_url, headers=headers, json=data, timeout=60)

            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]

                # 更新界面
                self.result_text.delete("1.0", tk.END)
                self.result_text.insert("1.0", content)
                self.status_label.configure(text="✅ 分析完成")
            else:
                self.status_label.configure(text="❌ 分析失败")
                self.result_text.delete("1.0", tk.END)
                self.result_text.insert("1.0", f"API调用失败: {response.status_code}")

        except Exception as e:
            self.status_label.configure(text="❌ 分析异常")
            self.result_text.delete("1.0", tk.END)
            self.result_text.insert("1.0", f"分析异常: {str(e)}")

        finally:
            # 恢复按钮
            self.analyze_btn.configure(state="normal", text="🚀 开始分析")

    def on_closing(self):
        """关闭事件"""
        self.root.destroy()

    def run(self):
        """运行应用"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = SimpleImageComparisonTool()
        app.run()
    except Exception as e:
        print(f"启动失败: {e}")
        # 尝试启动原版
        try:
            import subprocess
            subprocess.run([sys.executable, "image_comparison_tool.py"])
        except:
            print("无法启动任何版本")

if __name__ == "__main__":
    main()