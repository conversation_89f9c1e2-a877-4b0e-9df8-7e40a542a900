#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能启动脚本 - 自动选择最佳版本
"""

import sys
import os
import traceback

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🖼️  智能图像对比分析工具")
    print("    Enhanced Edition v2.0")
    print("")
    print("    基于 Grok-2-Vision API 的高级图像分析")
    print("=" * 60)
    print()

def check_customtkinter():
    """检查CustomTkinter是否可用"""
    try:
        import customtkinter as ctk
        
        # 尝试创建一个简单的测试窗口
        root = ctk.CTk()
        root.withdraw()  # 隐藏窗口
        
        # 测试基本功能
        test_label = ctk.CTkLabel(root, text="test")
        
        # 立即销毁测试窗口
        root.destroy()
        
        print("✅ CustomTkinter 可用 - 将启动现代化版本")
        return True
        
    except Exception as e:
        print(f"⚠️  CustomTkinter 不可用: {e}")
        print("📝 将回退到原版界面")
        return False

def start_simple_version():
    """启动简化版现代化版本"""
    try:
        print("🚀 启动简化版现代化版本...")

        # 设置环境
        os.environ['CTK_DISABLE_DPI_AWARENESS'] = '1'  # 禁用DPI感知

        import image_comparison_tool_simple
        app = image_comparison_tool_simple.SimpleImageComparisonTool()
        app.run()
        return True

    except Exception as e:
        print(f"❌ 简化版启动失败: {e}")
        return False

def start_modern_version():
    """启动完整现代化版本"""
    try:
        print("🚀 启动完整现代化版本...")

        # 设置环境
        os.environ['CTK_DISABLE_DPI_AWARENESS'] = '1'  # 禁用DPI感知

        import image_comparison_tool_modern
        app = image_comparison_tool_modern.ModernImageComparisonTool()
        app.run()
        return True

    except Exception as e:
        print(f"❌ 完整现代化版本启动失败: {e}")
        return False

def start_original_version():
    """启动原版"""
    try:
        print("🚀 启动原版...")
        
        if not os.path.exists("image_comparison_tool.py"):
            print("❌ 原版文件不存在")
            return False
        
        import image_comparison_tool
        import tkinter as tk
        
        root = tk.Tk()
        app = image_comparison_tool.ImageComparisonTool(root)
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"❌ 原版启动失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        input("按回车键退出...")
        return False
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    
    # 检查基础依赖
    try:
        import tkinter
        import requests
        from PIL import Image
        print("✅ 基础依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少基础依赖: {e}")
        print("请运行: pip install requests Pillow")
        input("按回车键退出...")
        return False
    
    # 尝试现代化版本
    if check_customtkinter():
        # 先尝试简化版
        if start_simple_version():
            return True
        else:
            print("⚠️  简化版启动失败，尝试完整版...")
            if start_modern_version():
                return True
            else:
                print("⚠️  所有现代化版本都启动失败，尝试原版...")

    # 回退到原版
    if start_original_version():
        return True
    
    # 都失败了
    print("❌ 所有版本都启动失败")
    print("\n可能的解决方案:")
    print("1. 重新安装依赖: pip install --upgrade customtkinter requests Pillow")
    print("2. 检查系统是否支持GUI应用")
    print("3. 尝试以管理员身份运行")
    print("4. 检查防火墙和杀毒软件设置")
    
    input("\n按回车键退出...")
    return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n未预期的错误: {e}")
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
