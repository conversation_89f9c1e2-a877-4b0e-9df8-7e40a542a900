@echo off
chcp 65001 >nul
title 智能图像对比分析工具 - Enhanced Edition

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🖼️  智能图像对比分析工具                      ║
echo ║                    Enhanced Edition v2.0                    ║
echo ║                                                              ║
echo ║              基于 Grok-2-Vision API 的高级图像分析          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 🔍 检查依赖包...
python -c "import customtkinter, requests, PIL" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  检测到缺少依赖包，正在自动安装...
    echo.
    echo 📦 安装 customtkinter...
    pip install customtkinter
    echo 📦 安装 requests...
    pip install requests
    echo 📦 安装 Pillow...
    pip install Pillow
    echo 📦 安装 darkdetect...
    pip install darkdetect
    echo.
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包检查通过
)

echo.
echo 🚀 启动增强版图像对比工具...
echo.

python image_comparison_tool_modern.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    echo 请检查错误信息并重试
    pause
) else (
    echo.
    echo ✅ 程序正常退出
)

pause
