@echo off
chcp 65001 >nul 2>&1
title 智能图像对比分析工具 - Enhanced Edition

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                🖼️  智能图像对比分析工具                      ║
echo ║                    Enhanced Edition v2.0                    ║
echo ║                                                              ║
echo ║              基于 Grok-2-Vision API 的高级图像分析          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 检查当前目录...
if not exist "image_comparison_tool_modern.py" (
    echo ❌ 错误: 找不到主程序文件
    echo 请确保在正确的目录下运行此脚本
    echo 当前目录: %CD%
    pause
    exit /b 1
)
echo ✅ 主程序文件存在

echo.
echo 🔍 检查Python环境...
python --version 2>nul
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)
echo ✅ Python环境检查通过

echo.
echo 🔍 检查代码语法...
python -m py_compile image_comparison_tool_modern.py 2>error.log
if errorlevel 1 (
    echo ❌ 错误: 代码语法检查失败
    echo 错误详情:
    type error.log
    del error.log 2>nul
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)
del error.log 2>nul
echo ✅ 代码语法检查通过

echo.
echo 🔍 检查依赖包...
python -c "import customtkinter, requests, PIL, tkinter, json, threading, datetime, base64, os" 2>nul
if errorlevel 1 (
    echo ⚠️  检测到缺少依赖包，正在自动安装...
    echo.
    echo 📦 安装依赖包...
    pip install customtkinter requests Pillow darkdetect
    if errorlevel 1 (
        echo ❌ 依赖包安装失败
        echo 请手动运行: pip install customtkinter requests Pillow darkdetect
        echo.
        echo 按任意键退出...
        pause >nul
        exit /b 1
    )
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包检查通过
)

echo.
echo 🚀 启动智能图像对比工具...
echo 系统将自动选择最佳版本
echo.

python smart_start.py 2>error.log

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    echo 错误详情:
    if exist error.log (
        type error.log
        del error.log
    )
    echo.
    echo 可能的解决方案:
    echo 1. 检查Python版本是否为3.8+
    echo 2. 重新安装依赖包: pip install --upgrade customtkinter requests Pillow
    echo 3. 检查系统是否支持GUI应用程序
    echo 4. 尝试以管理员身份运行
    echo.
    echo 按任意键退出...
    pause >nul
) else (
    echo.
    echo ✅ 程序正常退出
    del error.log 2>nul
)

echo.
echo 按任意键关闭窗口...
pause >nul
