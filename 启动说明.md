# 🖼️ 图像对比工具启动说明

## 🚀 快速启动

### 方法1：使用批处理文件（推荐）
双击运行 `start_enhanced.bat`

### 方法2：使用Python脚本
```bash
python smart_start.py
```

### 方法3：直接启动特定版本
```bash
# 简化版现代化界面（推荐）
python image_comparison_tool_simple.py

# 完整版现代化界面
python image_comparison_tool_modern.py

# 原版界面
python image_comparison_tool.py
```

## 📋 版本说明

### 🌟 简化版现代化界面 (推荐)
- **文件**: `image_comparison_tool_simple.py`
- **特点**: 现代化界面，兼容性最好
- **功能**: 核心功能完整，界面简洁美观
- **适用**: 大多数用户，解决了DPI缩放问题

### 🎨 完整版现代化界面
- **文件**: `image_comparison_tool_modern.py`
- **特点**: 功能最全，界面最美观
- **功能**: 拖拽、快捷键、预设提示词等高级功能
- **适用**: 高端用户，需要系统兼容性好

### 📝 原版界面
- **文件**: `image_comparison_tool.py`
- **特点**: 稳定可靠，兼容性最好
- **功能**: 基础功能完整
- **适用**: 所有用户，作为备用方案

## 🔧 故障排除

### 问题1：启动脚本闪退
**解决方案**:
1. 右键点击 `start_enhanced.bat`，选择"以管理员身份运行"
2. 或者直接运行 `python smart_start.py`

### 问题2：CustomTkinter相关错误
**解决方案**:
1. 系统会自动回退到原版界面
2. 或手动运行 `python image_comparison_tool.py`

### 问题3：依赖包缺失
**解决方案**:
```bash
pip install customtkinter requests Pillow darkdetect
```

### 问题4：DPI缩放问题
**解决方案**:
1. 使用简化版: `python image_comparison_tool_simple.py`
2. 或在系统设置中调整DPI缩放

### 问题5：GUI无法显示
**解决方案**:
1. 检查是否在远程桌面或服务器环境
2. 确保系统支持GUI应用程序
3. 检查防火墙和杀毒软件设置

## 🎯 使用建议

### 首次使用
1. 双击 `start_enhanced.bat`
2. 系统会自动选择最佳版本
3. 如果有问题，会自动回退

### 日常使用
- **推荐**: 简化版现代化界面
- **备用**: 原版界面
- **高级**: 完整版现代化界面

### 配置API密钥
1. 在界面中输入您的Grok API密钥
2. 密钥格式: `xai-xxxxxxxxxx`
3. 配置会自动保存到 `config.json`

## 📁 文件说明

| 文件 | 说明 | 推荐度 |
|------|------|--------|
| `start_enhanced.bat` | 智能启动脚本 | ⭐⭐⭐⭐⭐ |
| `smart_start.py` | Python智能启动 | ⭐⭐⭐⭐ |
| `image_comparison_tool_simple.py` | 简化版现代化界面 | ⭐⭐⭐⭐⭐ |
| `image_comparison_tool_modern.py` | 完整版现代化界面 | ⭐⭐⭐⭐ |
| `image_comparison_tool.py` | 原版界面 | ⭐⭐⭐ |
| `config.json` | 配置文件 | - |

## 🆘 获取帮助

如果遇到问题：
1. 查看控制台错误信息
2. 尝试不同版本
3. 检查Python和依赖包版本
4. 确保API密钥正确

---

**祝您使用愉快！** 🎉
